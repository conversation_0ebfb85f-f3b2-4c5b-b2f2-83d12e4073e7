import {type ActionFunctionArgs} from '@shopify/remix-oxygen';

// Type definitions
interface ActionData {
  success?: boolean;
  error?: string;
}

// Use the non-deprecated json function
const json = (data: any, init?: ResponseInit) => {
  return new Response(JSON.stringify(data), {
    ...init,
    headers: {
      'Content-Type': 'application/json',
      ...init?.headers,
    },
  });
};

export async function action({request, context}: ActionFunctionArgs): Promise<Response> {
  // Only allow POST requests
  if (request.method !== 'POST') {
    return json({
      success: false,
      error: 'Method not allowed'
    }, { status: 405 });
  }

  try {
    // Parse form data
    const formData = await request.formData();
    const email = formData.get('email') as string;

    // Basic validation
    if (!email || !email.trim()) {
      return json({
        success: false,
        error: 'Email address is required'
      }, { status: 400 });
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      return json({
        success: false,
        error: 'Please enter a valid email address'
      }, { status: 400 });
    }

    // Get environment variables
    const apiKey = context.env.MAILCHIMP_API_KEY;
    const serverPrefix = context.env.MAILCHIMP_SERVER_PREFIX;
    const listId = context.env.MAILCHIMP_LIST_ID;

    if (!apiKey || !serverPrefix || !listId) {
      console.error('Missing Mailchimp configuration:', {
        hasApiKey: !!apiKey,
        hasServerPrefix: !!serverPrefix,
        hasListId: !!listId
      });
      return json({
        success: false,
        error: 'Newsletter service is temporarily unavailable'
      }, { status: 500 });
    }

    // Initialize Mailchimp client
    const mailchimp = require('@mailchimp/mailchimp_marketing');
    
    mailchimp.setConfig({
      apiKey: apiKey,
      server: serverPrefix,
    });

    // Add subscriber to list
    const response = await mailchimp.lists.addListMember(listId, {
      email_address: email.trim().toLowerCase(),
      status: 'subscribed',
      merge_fields: {
        SOURCE: 'Website Newsletter'
      }
    });

    console.log('Successfully added subscriber:', response.email_address);

    return json({
      success: true
    });

  } catch (error: any) {
    console.error('Newsletter signup error:', error);

    // Handle Mailchimp-specific errors
    if (error.response && error.response.body) {
      const errorBody = error.response.body;
      
      // Handle already subscribed
      if (errorBody.title === 'Member Exists') {
        return json({
          success: false,
          error: 'This email is already subscribed to our newsletter'
        }, { status: 400 });
      }

      // Handle invalid email
      if (errorBody.title === 'Invalid Resource') {
        return json({
          success: false,
          error: 'Please enter a valid email address'
        }, { status: 400 });
      }

      // Handle other Mailchimp errors
      return json({
        success: false,
        error: errorBody.detail || 'Failed to subscribe. Please try again.'
      }, { status: 400 });
    }

    // Generic error handling
    return json({
      success: false,
      error: 'Unable to subscribe at this time. Please try again later.'
    }, { status: 500 });
  }
}
