import {type ActionFunctionArgs} from '@shopify/remix-oxygen';

// Type definitions
interface ActionData {
  success?: boolean;
  error?: string;
}

// Use the non-deprecated json function
const json = (data: any, init?: ResponseInit) => {
  return new Response(JSON.stringify(data), {
    ...init,
    headers: {
      'Content-Type': 'application/json',
      ...init?.headers,
    },
  });
};

export async function action({request, context}: ActionFunctionArgs): Promise<Response> {
  // Only allow POST requests
  if (request.method !== 'POST') {
    return json({
      success: false,
      error: 'Method not allowed'
    }, { status: 405 });
  }

  try {
    // Parse form data
    const formData = await request.formData();
    const email = formData.get('email') as string;

    // Basic validation
    if (!email || !email.trim()) {
      return json({
        success: false,
        error: 'Email address is required'
      }, { status: 400 });
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      return json({
        success: false,
        error: 'Please enter a valid email address'
      }, { status: 400 });
    }

    // Get environment variables
    const apiKey = context.env.MAILCHIMP_API_KEY;
    const serverPrefix = context.env.MAILCHIMP_SERVER_PREFIX;
    const listId = context.env.MAILCHIMP_LIST_ID;

    if (!apiKey || !serverPrefix || !listId) {
      console.error('Missing Mailchimp configuration:', {
        hasApiKey: !!apiKey,
        hasServerPrefix: !!serverPrefix,
        hasListId: !!listId
      });
      return json({
        success: false,
        error: 'Newsletter service is temporarily unavailable'
      }, { status: 500 });
    }

    // Make direct API call to Mailchimp
    const mailchimpUrl = `https://${serverPrefix}.api.mailchimp.com/3.0/lists/${listId}/members`;

    const mailchimpResponse = await fetch(mailchimpUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${Buffer.from(`anystring:${apiKey}`).toString('base64')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email_address: email.trim().toLowerCase(),
        status: 'subscribed',
        merge_fields: {
          SOURCE: 'Website Newsletter'
        }
      })
    });

    const responseData = await mailchimpResponse.json();

    if (!mailchimpResponse.ok) {
      // Handle Mailchimp API errors
      console.error('Mailchimp API error:', responseData);

      if (responseData.title === 'Member Exists') {
        return json({
          success: false,
          error: 'This email is already subscribed to our newsletter'
        }, { status: 400 });
      }

      if (responseData.title === 'Invalid Resource') {
        return json({
          success: false,
          error: 'Please enter a valid email address'
        }, { status: 400 });
      }

      return json({
        success: false,
        error: responseData.detail || 'Failed to subscribe. Please try again.'
      }, { status: 400 });
    }

    console.log('Successfully added subscriber:', responseData.email_address);

    return json({
      success: true
    });

  } catch (error: any) {
    console.error('Newsletter signup error:', error);

    // Generic error handling
    return json({
      success: false,
      error: 'Unable to subscribe at this time. Please try again later.'
    }, { status: 500 });
  }
}
