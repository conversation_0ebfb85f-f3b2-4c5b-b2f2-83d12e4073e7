{"name": "@mailchimp/mailchimp_marketing", "version": "3.0.80", "description": "The official Node client library for the Mailchimp Marketing API", "license": "Apache 2.0", "main": "src/index.js", "scripts": {"test": "jest --setupFiles dotenv/config"}, "browser": {"fs": false}, "dependencies": {"superagent": "3.8.1", "dotenv": "^8.2.0"}, "devDependencies": {"mocha": "~2.3.4", "sinon": "1.17.3", "jest": "^26.2.2"}, "homepage": "https://github.com/mailchimp/mailchimp-marketing-node", "repository": {"type": "git", "url": "git+https://github.com/mailchimp/mailchimp-marketing-node.git"}, "bugs": {"url": "https://github.com/mailchimp/mailchimp-client-lib-codegen/issues"}, "keywords": ["mailchimp", "api", "v3"], "engines": {"node": ">=10.0.0"}}