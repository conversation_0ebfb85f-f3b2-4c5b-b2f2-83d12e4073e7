{"name": "formidable", "version": "1.2.6", "license": "MIT", "description": "(DEPRECATED! Install formidable@v2) A node.js module for parsing form data, especially file uploads.", "homepage": "https://github.com/node-formidable/formidable", "funding": "https://ko-fi.com/tunnckoCore/commissions", "repository": "node-formidable/formidable", "main": "./lib/index.js", "files": ["lib"], "publishConfig": {"access": "public", "tag": "v1"}, "devDependencies": {"gently": "^0.8.0", "findit": "^0.1.2", "hashish": "^0.0.4", "urun": "^0.0.6", "utest": "^0.0.8", "request": "^2.11.4"}, "scripts": {"test": "node test/run.js", "clean": "rm test/tmp/*"}}