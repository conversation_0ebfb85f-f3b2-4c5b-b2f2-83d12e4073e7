import {Form, useActionData, useNavigation} from 'react-router';
import {useState, useEffect} from 'react';

interface ActionData {
  success?: boolean;
  error?: string;
}

interface NewsletterSignupProps {
  className?: string;
}

export function NewsletterSignup({className = ''}: NewsletterSignupProps) {
  const actionData = useActionData() as ActionData;
  const navigation = useNavigation();
  const [email, setEmail] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);

  const isSubmitting = navigation.state === 'submitting' && 
    navigation.formAction === '/api/newsletter';

  // Handle success state
  useEffect(() => {
    if (actionData?.success) {
      setShowSuccess(true);
      setEmail('');
      // Hide success message after 5 seconds
      const timer = setTimeout(() => {
        setShowSuccess(false);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [actionData?.success]);

  // Reset success state when user starts typing again
  useEffect(() => {
    if (showSuccess && email) {
      setShowSuccess(false);
    }
  }, [email, showSuccess]);

  return (
    <div className={`relative bg-gradient-to-r from-army-600 to-army-700 text-white overflow-hidden ${className}`}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-6">
          {/* Left side - Enhanced text content */}
          <div className="flex-1 text-center lg:text-left">
            <div className="flex items-center justify-center lg:justify-start gap-3 mb-3">
              <div className="bg-orange-500 p-2 rounded-full">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                </svg>
              </div>
              <h3 className="text-xl font-bold">
                Join Our Coffee Community
              </h3>
            </div>
            <p className="text-2xl font-bold text-orange-300 mb-2">
              Get 5% Off Your First Order!
            </p>
            <p className="text-army-100 text-base leading-relaxed max-w-md mx-auto lg:mx-0">
              Stay updated with new coffee releases, brewing tips, exclusive offers, and behind-the-scenes stories from our mountain roastery.
            </p>
          </div>

          {/* Right side - Enhanced form */}
          <div className="flex-shrink-0 w-full lg:w-auto lg:min-w-[400px]">
            {showSuccess ? (
              <div className="bg-gradient-to-r from-green-500 to-green-600 text-white px-8 py-6 rounded-xl text-center shadow-lg border border-green-400">
                <div className="flex items-center justify-center gap-3 mb-2">
                  <div className="bg-white bg-opacity-20 p-2 rounded-full">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="font-bold text-lg">Welcome to the Community!</span>
                </div>
                <p className="text-green-100 font-medium">
                  Check your email for your 5% off discount code and brewing tips!
                </p>
              </div>
            ) : (
              <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6 border border-white border-opacity-20 shadow-lg">
                <Form
                  method="post"
                  action="/api/newsletter"
                  className="space-y-4"
                >
                  <div className="relative">
                    <input
                      type="email"
                      name="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your email address"
                      required
                      disabled={isSubmitting}
                      className="w-full px-5 py-4 rounded-xl border-2 border-gray-200 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:cursor-not-allowed text-lg shadow-sm transition-all duration-200"
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                      <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                      </svg>
                    </div>
                  </div>
                  <button
                    type="submit"
                    disabled={isSubmitting || !email.trim()}
                    className="w-full px-8 py-4 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 focus:ring-offset-army-600 shadow-lg transform hover:scale-105 active:scale-95"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center justify-center gap-3">
                        <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Joining the Community...</span>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center gap-2">
                        <span>Claim Your 5% Discount</span>
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </button>
                </Form>
              </div>
            )}

            {/* Enhanced error message */}
            {actionData?.error && !showSuccess && (
              <div className="mt-4 bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-4 rounded-xl shadow-lg border border-red-400">
                <div className="flex items-center gap-3">
                  <div className="bg-white bg-opacity-20 p-1 rounded-full flex-shrink-0">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="font-medium">{actionData.error}</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
