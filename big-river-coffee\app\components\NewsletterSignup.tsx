import {Form, useActionData, useNavigation} from 'react-router';
import {useState, useEffect} from 'react';

interface ActionData {
  success?: boolean;
  error?: string;
}

interface NewsletterSignupProps {
  className?: string;
}

export function NewsletterSignup({className = ''}: NewsletterSignupProps) {
  const actionData = useActionData() as ActionData;
  const navigation = useNavigation();
  const [email, setEmail] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);

  const isSubmitting = navigation.state === 'submitting' && 
    navigation.formAction === '/api/newsletter';

  // Handle success state
  useEffect(() => {
    if (actionData?.success) {
      setShowSuccess(true);
      setEmail('');
      // Hide success message after 5 seconds
      const timer = setTimeout(() => {
        setShowSuccess(false);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [actionData?.success]);

  // Reset success state when user starts typing again
  useEffect(() => {
    if (showSuccess && email) {
      setShowSuccess(false);
    }
  }, [email, showSuccess]);

  return (
    <div className={`bg-army-600 text-white ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex flex-col md:flex-row items-center justify-between gap-4">
          {/* Left side - Text content */}
          <div className="flex-1 text-center md:text-left">
            <h3 className="text-lg font-semibold mb-1">
              Join Our Newsletter & Get 5% Off Your First Order!
            </h3>
            <p className="text-army-100 text-sm">
              Stay updated with new coffee releases, brewing tips, and exclusive offers.
            </p>
          </div>

          {/* Right side - Form */}
          <div className="flex-shrink-0 w-full md:w-auto">
            {showSuccess ? (
              <div className="bg-green-600 text-white px-6 py-3 rounded-lg text-center">
                <div className="flex items-center justify-center gap-2">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="font-medium">Successfully subscribed!</span>
                </div>
                <p className="text-sm mt-1 text-green-100">
                  Check your email for your 5% off discount code.
                </p>
              </div>
            ) : (
              <Form 
                method="post" 
                action="/api/newsletter"
                className="flex flex-col sm:flex-row gap-3 w-full md:w-auto"
              >
                <div className="flex-1 min-w-0">
                  <input
                    type="email"
                    name="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email address"
                    required
                    disabled={isSubmitting}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                  />
                </div>
                <button
                  type="submit"
                  disabled={isSubmitting || !email.trim()}
                  className="px-6 py-3 bg-orange-600 hover:bg-orange-700 text-white font-semibold rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 focus:ring-offset-army-600 whitespace-nowrap"
                >
                  {isSubmitting ? (
                    <div className="flex items-center gap-2">
                      <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>Subscribing...</span>
                    </div>
                  ) : (
                    'Get 5% Off'
                  )}
                </button>
              </Form>
            )}

            {/* Error message */}
            {actionData?.error && !showSuccess && (
              <div className="mt-3 bg-red-600 text-white px-4 py-2 rounded-lg text-sm">
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <span>{actionData.error}</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
